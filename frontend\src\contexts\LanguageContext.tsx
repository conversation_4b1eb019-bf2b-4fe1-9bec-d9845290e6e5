import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'vi' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string) => string;
}

// Translation dictionary
const translations = {
  vi: {
    // Navigation
    'nav.home': 'Trang chủ',
    'nav.events': 'Sự kiện',
    'nav.campaigns': 'Chiến dịch',
    'nav.posts': 'Bài viết',
    'nav.profile': 'Tài khoản',
    'nav.settings': 'Cài đặt',
    'nav.dashboard': 'Bảng điều khiển',
    'nav.admin': 'Quản trị viên',
    'nav.login': 'Đăng nhập',
    'nav.register': 'Đăng ký',
    'nav.logout': 'Đăng xuất',

    // Settings
    'settings.title': 'Cài đặt',
    'settings.subtitle': '<PERSON>uản lý thông tin tài khoản và tùy chọn cá nhân của bạn',
    'settings.general': 'Thông tin chung',
    'settings.account': '<PERSON><PERSON><PERSON> kho<PERSON>n',
    'settings.security': 'Bảo mật',
    'settings.notifications': 'Thông báo',
    'settings.privacy': 'Quyền riêng tư',
    'settings.theme': 'Giao diện',
    'settings.language': 'Ngôn ngữ',
    'settings.theme.light': '🌞 Sáng',
    'settings.theme.dark': '🌙 Tối',
    'settings.language.vi': '🇻🇳 Tiếng Việt',
    'settings.language.en': '🇺🇸 English',
    'settings.save': 'Lưu thay đổi',
    'settings.saving': 'Đang lưu...',
    'settings.displayOptions': 'Tùy chọn hiển thị',

    // Profile
    'profile.name': 'Họ và tên',
    'profile.email': 'Email',
    'profile.phone': 'Số điện thoại',
    'profile.address': 'Địa chỉ',
    'profile.bio': 'Giới thiệu bản thân',
    'profile.avatar': 'Ảnh đại diện',

    // Security
    'security.changePassword': 'Đổi mật khẩu',
    'security.currentPassword': 'Mật khẩu hiện tại',
    'security.newPassword': 'Mật khẩu mới',
    'security.confirmPassword': 'Xác nhận mật khẩu mới',
    'security.loginSessions': 'Phiên đăng nhập',
    'security.currentDevice': 'Thiết bị hiện tại',
    'security.active': 'Đang hoạt động',

    // Notifications
    'notifications.emailNotifications': 'Thông báo email',
    'notifications.pushNotifications': 'Thông báo đẩy',
    'notifications.generalNotifications': 'Thông báo chung',
    'notifications.emailDesc': 'Nhận email về các hoạt động quan trọng',
    'notifications.pushDesc': 'Nhận thông báo đẩy trên trình duyệt',

    // Privacy
    'privacy.profileVisibility': 'Hiển thị hồ sơ',
    'privacy.showEmail': 'Hiển thị email',
    'privacy.showPhone': 'Hiển thị số điện thoại',
    'privacy.allowMessages': 'Cho phép nhắn tin',
    'privacy.allowFriendRequests': 'Cho phép kết bạn',
    'privacy.public': '🌍 Công khai - Mọi người',
    'privacy.friends': '👥 Bạn bè',
    'privacy.private': '🔒 Riêng tư - Chỉ mình tôi',

    // Account
    'account.loginEmail': 'Email đăng nhập',
    'account.emailDesc': 'Email này được sử dụng để đăng nhập và nhận thông báo',
    'account.exportData': 'Xuất dữ liệu cá nhân',
    'account.exportDesc': 'Tải xuống bản sao dữ liệu cá nhân của bạn',
    'account.deleteAccount': 'Xóa tài khoản',
    'account.deleteDesc': 'Xóa vĩnh viễn tài khoản và tất cả dữ liệu liên quan. Hành động này không thể hoàn tác.',

    // Common
    'common.save': 'Lưu',
    'common.cancel': 'Hủy',
    'common.delete': 'Xóa',
    'common.edit': 'Chỉnh sửa',
    'common.loading': 'Đang tải...',
    'common.success': 'Thành công',
    'common.error': 'Lỗi',
    'common.change': 'Thay đổi',

    // Messages
    'message.updateSuccess': 'Cập nhật thông tin thành công!',
    'message.updateError': 'Có lỗi xảy ra khi cập nhật thông tin',
    'message.passwordMismatch': 'Mật khẩu xác nhận không khớp',
    'message.passwordTooShort': 'Mật khẩu mới phải có ít nhất 6 ký tự',
    'message.passwordChangeSuccess': 'Đổi mật khẩu thành công!',
    'message.loginRequired': 'Vui lòng đăng nhập để xem bài viết',
    'message.checkingAuth': 'Đang kiểm tra đăng nhập...',

    'common.view': 'Xem',
    'common.search': 'Tìm kiếm',
    'common.filter': 'Lọc',
    'common.all': 'Tất cả',
    'common.yes': 'Có',
    'common.no': 'Không',
    'common.confirm': 'Xác nhận',
    'common.back': 'Quay lại',
    'common.next': 'Tiếp theo',
    'common.previous': 'Trước',
    'common.submit': 'Gửi',
    'common.close': 'Đóng',

    // Homepage
    'home.title': 'Trang chủ',
    'home.welcome': 'Chào mừng đến với KeyDyWeb',
    'home.description': 'Nền tảng thiện nguyện kết nối cộng đồng',
    'home.featuredEvents': 'Sự kiện nổi bật',
    'home.featuredCampaigns': 'Chiến dịch nổi bật',
    'home.statistics': 'Thống kê',
    'home.totalEvents': 'Sự kiện',
    'home.totalCampaigns': 'Chiến dịch',
    'home.totalDonations': 'Lượt quyên góp',
    'home.totalVolunteers': 'Tình nguyện viên',

    // Events
    'events.title': 'Sự kiện thiện nguyện',
    'events.upcoming': 'Sắp diễn ra',
    'events.ongoing': 'Đang diễn ra',
    'events.completed': 'Đã kết thúc',
    'events.cancelled': 'Đã hủy',
    'events.register': 'Đăng ký tham gia',
    'events.viewDetails': 'Xem chi tiết',
    'events.participants': 'người tham gia',
    'events.location': 'Địa điểm',
    'events.date': 'Ngày diễn ra',
    'events.deadline': 'Hạn đăng ký',

    // Campaigns
    'campaigns.title': 'Chiến dịch thiện nguyện',
    'campaigns.active': 'Đang quyên góp',
    'campaigns.completed': 'Đã hoàn thành',
    'campaigns.donate': 'Quyên góp',
    'campaigns.target': 'Mục tiêu',
    'campaigns.raised': 'Đã quyên góp',
    'campaigns.donors': 'người ủng hộ',
    'campaigns.progress': 'Tiến độ',

    // Posts
    'posts.title': 'Bài viết cộng đồng',
    'posts.create': 'Tạo bài viết',
    'posts.share': 'Chia sẻ',
    'posts.comment': 'Bình luận',
    'posts.like': 'Thích',
    'posts.reactions': 'Cảm xúc',

    // Profile
    'profile.title': 'Trang cá nhân',
    'profile.posts': 'Bài viết',
    'profile.about': 'Giới thiệu',
    'profile.activity': 'Hoạt động',
    'profile.badges': 'Huy hiệu',
    'profile.donations': 'Lần ủng hộ',
    'profile.events': 'Sự kiện',
    'profile.totalDonated': 'Đã ủng hộ',



    // Admin
    'admin.dashboard': 'Bảng điều khiển',
    'admin.users': 'Quản lý người dùng',
    'admin.events': 'Quản lý sự kiện',
    'admin.campaigns': 'Quản lý chiến dịch',
    'admin.posts': 'Quản lý bài viết',
    'admin.reports': 'Báo cáo',
    'admin.notifications': 'Thông báo',

    'nav.myEvents': 'Sự kiện của tôi',

    // Forms
    'form.name': 'Họ và tên',
    'form.email': 'Email',
    'form.phone': 'Số điện thoại',
    'form.password': 'Mật khẩu',
    'form.confirmPassword': 'Xác nhận mật khẩu',
    'form.message': 'Tin nhắn',
    'form.amount': 'Số tiền',
    'form.title': 'Tiêu đề',
    'form.description': 'Mô tả',
    'form.location': 'Địa điểm',
    'form.startDate': 'Ngày bắt đầu',
    'form.endDate': 'Ngày kết thúc',
    'form.category': 'Danh mục',
    'form.target': 'Mục tiêu',
    'form.maxParticipants': 'Số người tối đa',

    // Status
    'status.active': 'Đang hoạt động',
    'status.inactive': 'Không hoạt động',
    'status.pending': 'Đang chờ',
    'status.approved': 'Đã duyệt',
    'status.rejected': 'Đã từ chối',
    'status.completed': 'Đã hoàn thành',
    'status.cancelled': 'Đã hủy',

    // Notifications
    'notification.title': 'Thông báo',
    'notification.markAllRead': 'Đánh dấu tất cả đã đọc',
    'notification.noNotifications': 'Không có thông báo nào',
    'notification.newDonation': 'Quyên góp mới',
    'notification.newEvent': 'Sự kiện mới',
    'notification.eventReminder': 'Nhắc nhở sự kiện',

    // Errors
    'error.required': 'Trường này là bắt buộc',
    'error.invalidEmail': 'Email không hợp lệ',
    'error.passwordTooShort': 'Mật khẩu quá ngắn',
    'error.passwordMismatch': 'Mật khẩu không khớp',
    'error.networkError': 'Lỗi kết nối mạng',
    'error.serverError': 'Lỗi máy chủ',
    'error.notFound': 'Không tìm thấy',
    'error.unauthorized': 'Không có quyền truy cập',

    // Success messages
    'success.saved': 'Đã lưu thành công',
    'success.updated': 'Đã cập nhật thành công',
    'success.deleted': 'Đã xóa thành công',
    'success.created': 'Đã tạo thành công',
    'success.registered': 'Đã đăng ký thành công',
    'success.donated': 'Quyên góp thành công',

    // Time
    'time.today': 'Hôm nay',
    'time.yesterday': 'Hôm qua',
    'time.tomorrow': 'Ngày mai',
    'time.thisWeek': 'Tuần này',
    'time.thisMonth': 'Tháng này',
    'time.thisYear': 'Năm này',

    // Footer
    'footer.about': 'Về chúng tôi',
    'footer.contact': 'Liên hệ',
    'footer.privacy': 'Chính sách bảo mật',
    'footer.terms': 'Điều khoản sử dụng',
    'footer.faq': 'Câu hỏi thường gặp',
    'footer.guide': 'Hướng dẫn sử dụng',
    'footer.followUs': 'Theo dõi chúng tôi',
    'footer.allRightsReserved': 'Tất cả quyền được bảo lưu',
    'footer.sitemap': 'Sơ đồ trang web',

    // Homepage specific
    'home.featuredEventsDesc': 'Những sự kiện có nhiều người đăng ký tham gia hoặc có đánh giá tốt',
    'home.featuredCampaignsDesc': 'Những chiến dịch đã hoàn thành hoặc có lượt quyên góp cao, ưu tiên chiến dịch đang quyên góp',
    'home.tryAgain': 'Thử lại',
    'home.donationsList': 'Danh sách quyên góp',
    'home.volunteersList': 'Danh sách tình nguyện viên',
    'home.noDonations': 'Chưa có quyên góp nào',
    'home.noDonationsDesc': 'Danh sách quyên góp sẽ xuất hiện ở đây',
    'home.noVolunteers': 'Chưa có tình nguyện viên nào',
    'home.noVolunteersDesc': 'Danh sách tình nguyện viên sẽ xuất hiện ở đây',
    'home.campaign': 'Chiến dịch',
    'home.supporters': 'ủng hộ',
    'home.achieved': 'Đã đạt',
    'home.target': 'mục tiêu',
    'home.people': 'người',

    // Event status
    'events.statusUpcoming': 'Sắp diễn ra',
    'events.statusOngoing': 'Đang diễn ra',
    'events.statusCompleted': 'Đã kết thúc',

    // Campaign status
    'campaigns.statusActive': 'Đang quyên góp',
    'campaigns.statusCompleted': 'Đã hoàn thành',
    'campaigns.statusEnded': 'Đã kết thúc',

    // Events page specific
    'events.noEvents': 'Chưa có events nào',
    'events.noEventsDesc': 'Hiện tại chưa có events nào phù hợp với bộ lọc của bạn',
    'events.searchPlaceholder': 'Tìm kiếm events...',
    'events.allStatus': 'Tất cả trạng thái',
    'events.sortByDate': 'Sắp xếp theo ngày',
    'events.sortByNewest': 'Mới nhất',
    'events.sortByParticipants': 'Nhiều người tham gia',

    // Homepage banner/slider
    'banner.joinCommunity': 'Chung tay vì cộng đồng',
    'banner.joinDesc': 'Tham gia các sự kiện thiện nguyện, kết nối những tấm lòng nhân ái',
    'banner.learnMore': 'Tìm hiểu thêm',
    'banner.aboutUs': 'Về KeyDyWeb',
    'banner.aboutDesc': 'Nền tảng kết nối cộng đồng hàng đầu, lan tỏa yêu thương và tạo nên những thay đổi tích cực, bền vững cho xã hội Việt Nam',

    // About section
    'about.connectCommunity': 'Kết nối cộng đồng',
    'about.connectDesc': 'Tạo cầu nối vững chắc giữa những tấm lòng nhân ái với các hoạt động thiện nguyện ý nghĩa, xây dựng mạng lưới cộng đồng mạnh mẽ',
    'about.transparent': 'Minh bạch & Tin cậy',
    'about.transparentDesc': 'Đảm bảo tính minh bạch tuyệt đối trong mọi hoạt động quyên góp và sử dụng nguồn lực một cách hiệu quả, có trách nhiệm',
    'about.positiveImpact': 'Tác động tích cực',
    'about.positiveImpactDesc': 'Tạo ra những thay đổi tích cực và bền vững cho cộng đồng thông qua các hoạt động thiện nguyện có ý nghĩa sâu sắc',

    // Footer sections
    'footer.explore': 'Khám phá',
    'footer.support': 'Hỗ trợ',
    'footer.contact': 'Liên hệ',

    // Events specific
    'events.meaningful': 'Ý nghĩa',
    'events.participants': 'người tham gia',
    'events.registered': 'Đã đăng ký',
    'events.registrationDeadline': 'Hạn đăng ký',
    'events.sortByDate': 'Sắp xếp theo ngày',
    'events.sortByNewest': 'Mới nhất',
    'events.sortByParticipants': 'Nhiều người tham gia',

    // Common status and actions
    'status.all': 'Tất cả',
    'action.viewDetails': 'Xem chi tiết',
    'action.register': 'Đăng ký',
    'action.share': 'Chia sẻ',

    // Campaigns specific
    'campaigns.supportTitle': 'Ủng hộ chiến dịch thiện nguyện',
    'campaigns.supportDesc': 'Mỗi đóng góp là một hy vọng mới, cùng nhau xây dựng tương lai tốt đẹp',

    // Footer specific
    'footer.about': 'Giới thiệu',
    'footer.faq': 'Câu hỏi thường gặp',
    'footer.guide': 'Hướng dẫn',
    'footer.privacy': 'Chính sách bảo mật',
    'footer.terms': 'Điều khoản sử dụng',
    'footer.followUs': 'Theo dõi chúng tôi',
    'footer.allRightsReserved': 'Tất cả quyền được bảo lưu',
    'footer.sitemap': 'Sơ đồ trang web',
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.events': 'Events',
    'nav.campaigns': 'Campaigns',
    'nav.posts': 'Posts',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    'nav.dashboard': 'Dashboard',
    'nav.admin': 'Admin',
    'nav.login': 'Login',
    'nav.register': 'Register',
    'nav.logout': 'Logout',

    // Settings
    'settings.title': 'Settings',
    'settings.subtitle': 'Manage your account information and personal preferences',
    'settings.general': 'General',
    'settings.account': 'Account',
    'settings.security': 'Security',
    'settings.notifications': 'Notifications',
    'settings.privacy': 'Privacy',
    'settings.theme': 'Theme',
    'settings.language': 'Language',
    'settings.theme.light': '🌞 Light',
    'settings.theme.dark': '🌙 Dark',
    'settings.language.vi': '🇻🇳 Vietnamese',
    'settings.language.en': '🇺🇸 English',
    'settings.save': 'Save Changes',
    'settings.saving': 'Saving...',
    'settings.displayOptions': 'Display Options',

    // Profile
    'profile.name': 'Full Name',
    'profile.email': 'Email',
    'profile.phone': 'Phone Number',
    'profile.address': 'Address',
    'profile.bio': 'Bio',
    'profile.avatar': 'Avatar',

    // Security
    'security.changePassword': 'Change Password',
    'security.currentPassword': 'Current Password',
    'security.newPassword': 'New Password',
    'security.confirmPassword': 'Confirm New Password',
    'security.loginSessions': 'Login Sessions',
    'security.currentDevice': 'Current Device',
    'security.active': 'Active',

    // Notifications
    'notifications.emailNotifications': 'Email Notifications',
    'notifications.pushNotifications': 'Push Notifications',
    'notifications.generalNotifications': 'General Notifications',
    'notifications.emailDesc': 'Receive emails about important activities',
    'notifications.pushDesc': 'Receive push notifications in browser',

    // Privacy
    'privacy.profileVisibility': 'Profile Visibility',
    'privacy.showEmail': 'Show Email',
    'privacy.showPhone': 'Show Phone Number',
    'privacy.allowMessages': 'Allow Messages',
    'privacy.allowFriendRequests': 'Allow Friend Requests',
    'privacy.public': '🌍 Public - Everyone',
    'privacy.friends': '👥 Friends',
    'privacy.private': '🔒 Private - Only Me',

    // Account
    'account.loginEmail': 'Login Email',
    'account.emailDesc': 'This email is used for login and receiving notifications',
    'account.exportData': 'Export Personal Data',
    'account.exportDesc': 'Download a copy of your personal data',
    'account.deleteAccount': 'Delete Account',
    'account.deleteDesc': 'Permanently delete your account and all related data. This action cannot be undone.',

    // Common
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.loading': 'Loading...',
    'common.success': 'Success',
    'common.error': 'Error',
    'common.change': 'Change',

    // Messages
    'message.updateSuccess': 'Information updated successfully!',
    'message.updateError': 'An error occurred while updating information',
    'message.passwordMismatch': 'Password confirmation does not match',
    'message.passwordTooShort': 'New password must be at least 6 characters',
    'message.passwordChangeSuccess': 'Password changed successfully!',
    'message.loginRequired': 'Please login to view posts',
    'message.checkingAuth': 'Checking authentication...',

    'common.view': 'View',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.all': 'All',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.confirm': 'Confirm',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.submit': 'Submit',
    'common.close': 'Close',

    // Homepage
    'home.title': 'Home',
    'home.welcome': 'Welcome to KeyDyWeb',
    'home.description': 'Charity platform connecting communities',
    'home.featuredEvents': 'Featured Events',
    'home.featuredCampaigns': 'Featured Campaigns',
    'home.statistics': 'Statistics',
    'home.totalEvents': 'Events',
    'home.totalCampaigns': 'Campaigns',
    'home.totalDonations': 'Donations',
    'home.totalVolunteers': 'Volunteers',

    // Events
    'events.title': 'Charity Events',
    'events.upcoming': 'Upcoming',
    'events.ongoing': 'Ongoing',
    'events.completed': 'Completed',
    'events.cancelled': 'Cancelled',
    'events.register': 'Register',
    'events.viewDetails': 'View Details',
    'events.participants': 'participants',
    'events.location': 'Location',
    'events.date': 'Event Date',
    'events.deadline': 'Registration Deadline',

    // Campaigns
    'campaigns.title': 'Charity Campaigns',
    'campaigns.active': 'Active',
    'campaigns.completed': 'Completed',
    'campaigns.donate': 'Donate',
    'campaigns.target': 'Target',
    'campaigns.raised': 'Raised',
    'campaigns.donors': 'donors',
    'campaigns.progress': 'Progress',

    // Posts
    'posts.title': 'Community Posts',
    'posts.create': 'Create Post',
    'posts.share': 'Share',
    'posts.comment': 'Comment',
    'posts.like': 'Like',
    'posts.reactions': 'Reactions',

    // Profile
    'profile.title': 'Profile',
    'profile.posts': 'Posts',
    'profile.about': 'About',
    'profile.activity': 'Activity',
    'profile.badges': 'Badges',
    'profile.donations': 'Donations',
    'profile.events': 'Events',
    'profile.totalDonated': 'Total Donated',



    // Admin
    'admin.dashboard': 'Dashboard',
    'admin.users': 'User Management',
    'admin.events': 'Event Management',
    'admin.campaigns': 'Campaign Management',
    'admin.posts': 'Post Management',
    'admin.reports': 'Reports',
    'admin.notifications': 'Notifications',

    'nav.myEvents': 'My Events',

    // Forms
    'form.name': 'Full Name',
    'form.email': 'Email',
    'form.phone': 'Phone Number',
    'form.password': 'Password',
    'form.confirmPassword': 'Confirm Password',
    'form.message': 'Message',
    'form.amount': 'Amount',
    'form.title': 'Title',
    'form.description': 'Description',
    'form.location': 'Location',
    'form.startDate': 'Start Date',
    'form.endDate': 'End Date',
    'form.category': 'Category',
    'form.target': 'Target',
    'form.maxParticipants': 'Max Participants',

    // Status
    'status.active': 'Active',
    'status.inactive': 'Inactive',
    'status.pending': 'Pending',
    'status.approved': 'Approved',
    'status.rejected': 'Rejected',
    'status.completed': 'Completed',
    'status.cancelled': 'Cancelled',

    // Notifications
    'notification.title': 'Notifications',
    'notification.markAllRead': 'Mark All as Read',
    'notification.noNotifications': 'No notifications',
    'notification.newDonation': 'New Donation',
    'notification.newEvent': 'New Event',
    'notification.eventReminder': 'Event Reminder',

    // Errors
    'error.required': 'This field is required',
    'error.invalidEmail': 'Invalid email address',
    'error.passwordTooShort': 'Password is too short',
    'error.passwordMismatch': 'Passwords do not match',
    'error.networkError': 'Network error',
    'error.serverError': 'Server error',
    'error.notFound': 'Not found',
    'error.unauthorized': 'Unauthorized access',

    // Success messages
    'success.saved': 'Saved successfully',
    'success.updated': 'Updated successfully',
    'success.deleted': 'Deleted successfully',
    'success.created': 'Created successfully',
    'success.registered': 'Registered successfully',
    'success.donated': 'Donation successful',

    // Time
    'time.today': 'Today',
    'time.yesterday': 'Yesterday',
    'time.tomorrow': 'Tomorrow',
    'time.thisWeek': 'This Week',
    'time.thisMonth': 'This Month',
    'time.thisYear': 'This Year',

    // Footer
    'footer.about': 'About Us',
    'footer.contact': 'Contact',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',
    'footer.faq': 'FAQ',
    'footer.guide': 'User Guide',
    'footer.followUs': 'Follow Us',
    'footer.allRightsReserved': 'All rights reserved',
    'footer.sitemap': 'Sitemap',

    // Homepage specific
    'home.featuredEventsDesc': 'Events with high participation or good ratings',
    'home.featuredCampaignsDesc': 'Completed campaigns or those with high donations, prioritizing active campaigns',
    'home.tryAgain': 'Try Again',
    'home.donationsList': 'Donations List',
    'home.volunteersList': 'Volunteers List',
    'home.noDonations': 'No donations yet',
    'home.noDonationsDesc': 'Donation list will appear here',
    'home.noVolunteers': 'No volunteers yet',
    'home.noVolunteersDesc': 'Volunteer list will appear here',
    'home.campaign': 'Campaign',
    'home.supporters': 'supporters',
    'home.achieved': 'Achieved',
    'home.target': 'target',
    'home.people': 'people',

    // Event status
    'events.statusUpcoming': 'Upcoming',
    'events.statusOngoing': 'Ongoing',
    'events.statusCompleted': 'Completed',

    // Campaign status
    'campaigns.statusActive': 'Active Fundraising',
    'campaigns.statusCompleted': 'Completed',
    'campaigns.statusEnded': 'Ended',

    // Events page specific
    'events.noEvents': 'No events yet',
    'events.noEventsDesc': 'Currently no events match your filter criteria',
    'events.searchPlaceholder': 'Search events...',
    'events.allStatus': 'All status',
    'events.sortByDate': 'Sort by date',
    'events.sortByNewest': 'Newest',
    'events.sortByParticipants': 'Most participants',

    // Homepage banner/slider
    'banner.joinCommunity': 'Join hands for community',
    'banner.joinDesc': 'Participate in charity events, connect compassionate hearts',
    'banner.learnMore': 'Learn More',
    'banner.aboutUs': 'About KeyDyWeb',
    'banner.aboutDesc': 'Leading community connection platform, spreading love and creating positive, sustainable changes for Vietnamese society',

    // About section
    'about.connectCommunity': 'Connect Community',
    'about.connectDesc': 'Create strong bridges between compassionate hearts and meaningful charity activities, building a powerful community network',
    'about.transparent': 'Transparent & Trustworthy',
    'about.transparentDesc': 'Ensure absolute transparency in all donation activities and use resources efficiently and responsibly',
    'about.positiveImpact': 'Positive Impact',
    'about.positiveImpactDesc': 'Create positive and sustainable changes for the community through meaningful charity activities',

    // Footer sections
    'footer.explore': 'Explore',
    'footer.support': 'Support',
    'footer.contact': 'Contact',

    // Events specific
    'events.meaningful': 'Meaningful',
    'events.participants': 'participants',
    'events.registered': 'Registered',
    'events.registrationDeadline': 'Registration deadline',
    'events.sortByDate': 'Sort by date',
    'events.sortByNewest': 'Newest',
    'events.sortByParticipants': 'Most participants',

    // Common status and actions
    'status.all': 'All',
    'action.viewDetails': 'View Details',
    'action.register': 'Register',
    'action.share': 'Share',

    // Campaigns specific
    'campaigns.supportTitle': 'Support Charity Campaigns',
    'campaigns.supportDesc': 'Every contribution is a new hope, together building a better future',

    // Footer specific
    'footer.about': 'About',
    'footer.faq': 'FAQ',
    'footer.guide': 'Guide',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',
    'footer.followUs': 'Follow Us',
    'footer.allRightsReserved': 'All rights reserved',
    'footer.sitemap': 'Sitemap',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>(() => {
    // Check localStorage first
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'vi' || savedLanguage === 'en')) {
      return savedLanguage;
    }
    
    // Check browser language
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith('vi')) {
      return 'vi';
    }
    
    return 'vi'; // Default to Vietnamese
  });

  useEffect(() => {
    // Save to localStorage
    localStorage.setItem('language', language);
    
    // Update document language
    document.documentElement.lang = language;
    
    console.log('🌐 Language changed to:', language);
  }, [language]);

  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
  };

  const t = (key: string): string => {
    return (translations[language] as any)[key] || key;
  };

  const value = {
    language,
    setLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
