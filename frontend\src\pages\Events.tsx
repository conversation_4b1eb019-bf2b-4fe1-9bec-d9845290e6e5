import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';
import { useLanguage } from '../contexts/LanguageContext';
import api from '../services/api';
import {
  Calendar,
  MapPin,
  Users,
  Clock,
  Search,
  Filter,
  ChevronRight,
  Heart,
  Share2
} from 'lucide-react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface Event {
  _id: string;
  title: string;
  description: string;
  eventDate: string;
  registrationDeadline: string;
  location: string;
  maxParticipants: number;
  currentParticipants: number;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  images: string[];
  createdBy: {
    name: string;
    email: string;
  };
}

const Events: React.FC = () => {
  const { t } = useLanguage();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('eventDate');
  const [stats, setStats] = useState({
    totalEvents: 0,
    totalParticipants: 0
  });

  useEffect(() => {
    fetchEvents();
  }, [searchTerm, statusFilter, sortBy]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      console.log('📅 [Real API] Loading events data...');

      // Fetch real events data from API
      try {
        const response = await api.get('/api/events');

        if (response.data.success) {
          const apiEvents = response.data.data || [];
          console.log('✅ [Real API] Events loaded from server:', Array.isArray(apiEvents) ? apiEvents.length : 0);

          // Ensure apiEvents is an array
          if (!Array.isArray(apiEvents)) {
            console.warn('⚠️ API returned non-array data:', apiEvents);
            throw new Error('Dữ liệu sự kiện không hợp lệ');
          }

          let filteredEvents = apiEvents;

          // Apply filters
          if (searchTerm) {
            filteredEvents = filteredEvents.filter(event =>
              event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
              event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
              event.location.toLowerCase().includes(searchTerm.toLowerCase())
            );
          }

          if (statusFilter !== 'all') {
            filteredEvents = filteredEvents.filter(event => event.status === statusFilter);
          }

          // Apply sorting
          filteredEvents.sort((a, b) => {
            switch (sortBy) {
              case 'eventDate':
                return new Date(a.eventDate).getTime() - new Date(b.eventDate).getTime();
              case 'createdAt':
                return new Date(b.eventDate).getTime() - new Date(a.eventDate).getTime(); // Newer first
              case 'currentParticipants':
                return b.currentParticipants - a.currentParticipants; // More participants first
              default:
                return 0;
            }
          });

          setEvents(filteredEvents);

          // Calculate stats from all events (not just filtered)
          const totalParticipants = apiEvents.reduce((sum: number, event: Event) => sum + event.currentParticipants, 0);
          setStats({
            totalEvents: apiEvents.length,
            totalParticipants
          });

          console.log('✅ [Real API] Events loaded successfully:', filteredEvents.length);
        } else {
          throw new Error(response.data.message || 'Không thể tải danh sách sự kiện');
        }
      } catch (apiError: any) {
        console.warn('⚠️ API Error, using empty fallback:', apiError);

        // Fallback to empty data
        setEvents([]);
        setStats({
          totalEvents: 0,
          totalParticipants: 0
        });

        const errorMessage = apiError?.response?.data?.message || 'Không thể kết nối đến server';
        console.error(`❌ Events API Error: ${errorMessage}`);
        toast.error('Không thể tải danh sách sự kiện từ server');
      }

    } catch (error: any) {
      console.error('Error loading events:', error);
      toast.error('Không thể tải danh sách sự kiện');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-800';
      case 'ongoing': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'upcoming': return t('events.upcoming');
      case 'ongoing': return t('events.ongoing');
      case 'completed': return t('events.completed');
      case 'cancelled': return t('events.cancelled');
      default: return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProgressPercentage = (current: number, max: number) => {
    return Math.min((current / max) * 100, 100);
  };

  const canRegister = (event: Event) => {
    const now = new Date();
    const registrationDeadline = new Date(event.registrationDeadline);
    return event.status === 'upcoming' &&
           now < registrationDeadline &&
           event.currentParticipants < event.maxParticipants;
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="aspect-video bg-gray-200"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white opacity-10 rounded-full animate-pulse"></div>
          <div className="absolute top-32 right-20 w-16 h-16 bg-white opacity-10 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-white opacity-10 rounded-full animate-pulse delay-2000"></div>
          <div className="absolute bottom-32 right-1/3 w-8 h-8 bg-white opacity-10 rounded-full animate-pulse delay-3000"></div>
        </div>
        <div className="relative max-w-7xl mx-auto px-4 py-20">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm font-medium mb-6 backdrop-blur-sm">
              <Heart className="h-4 w-4 mr-2" />
              {t('home.description')}
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              {t('events.title')}
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300">
                {t('events.meaningful')}
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
              {t('home.featuredEventsDesc')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center text-lg">
                <Users className="h-5 w-5 mr-2" />
                <span>{stats.totalParticipants}+ {t('events.participants')}</span>
              </div>
              <div className="flex items-center text-lg">
                <Calendar className="h-5 w-5 mr-2" />
                <span>{stats.totalEvents} {t('home.totalEvents')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              id="events-search"
              name="search"
              type="text"
              placeholder={t('common.search') + ' ' + t('nav.events').toLowerCase() + '...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              autoComplete="off"
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <label htmlFor="status-filter" className="sr-only">{t('common.filter')}</label>
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <select
              id="status-filter"
              name="statusFilter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
            >
              <option value="all">{t('status.all')} {t('status.active').toLowerCase()}</option>
              <option value="upcoming">{t('events.statusUpcoming')}</option>
              <option value="ongoing">{t('events.statusOngoing')}</option>
              <option value="completed">{t('events.statusCompleted')}</option>
            </select>
          </div>

          {/* Sort */}
          <div>
            <label htmlFor="sort-by" className="sr-only">{t('events.sortByDate')}</label>
            <select
              id="sort-by"
              name="sortBy"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="eventDate">{t('events.date')}</option>
              <option value="createdAt">{t('events.sortByNewest')}</option>
              <option value="currentParticipants">{t('events.sortByParticipants')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Events Grid */}
      {events.length === 0 ? (
        <div className="text-center py-12">
          <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('events.noEvents')}</h3>
          <p className="text-gray-500">{t('events.noEventsDesc')}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {events.map((event) => (
            <div key={event._id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              {/* Event Image */}
              <div className="aspect-video bg-gradient-to-br from-blue-500 to-purple-600 relative">
                {event.images && event.images.length > 0 ? (
                  <img
                    src={event.images[0].startsWith('http') || event.images[0].startsWith('data:')
                      ? event.images[0]
                      : `${API_URL}/${event.images[0]}`}
                    alt={event.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.parentElement?.querySelector('.fallback-icon')?.classList.remove('hidden');
                    }}
                  />
                ) : null}

                {/* Fallback icon for both cases */}
                <div className={`w-full h-full flex items-center justify-center ${event.images && event.images.length > 0 ? 'hidden fallback-icon' : ''}`}>
                  <Heart className="h-16 w-16 text-white opacity-50" />
                </div>

                {/* Status Badge */}
                <div className="absolute top-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                    {getStatusText(event.status)}
                  </span>
                </div>

                {/* Share Button */}
                <button className="absolute top-4 right-4 p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors">
                  <Share2 className="h-4 w-4" />
                </button>
              </div>

              {/* Event Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                  {event.title}
                </h3>

                <p className="text-gray-600 mb-4 line-clamp-2">
                  {event.description}
                </p>

                {/* Event Details */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>{formatDate(event.eventDate)}</span>
                  </div>

                  <div className="flex items-center text-sm text-gray-500">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span className="line-clamp-1">{event.location}</span>
                  </div>

                  <div className="flex items-center text-sm text-gray-500">
                    <Users className="h-4 w-4 mr-2" />
                    <span>{event.currentParticipants}/{event.maxParticipants} {t('events.participants')}</span>
                  </div>

                  {canRegister(event) && (
                    <div className="flex items-center text-sm text-orange-600">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>{t('events.registrationDeadline')}: {formatDate(event.registrationDeadline)}</span>
                    </div>
                  )}
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>{t('events.registered')}</span>
                    <span>{getProgressPercentage(event.currentParticipants, event.maxParticipants).toFixed(0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${getProgressPercentage(event.currentParticipants, event.maxParticipants)}%` }}
                    ></div>
                  </div>
                </div>

                {/* Action Button */}
                <Link
                  to={`/events/${event._id}`}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center group shadow-md hover:shadow-lg"
                >
                  <span>{t('action.viewDetails')}</span>
                  <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
      </div>
    </div>
  );
};

export default Events;