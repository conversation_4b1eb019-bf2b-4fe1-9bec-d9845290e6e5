import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Slider from '../components/ui/slider';
import { formatCurrency } from '../utils/format';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import {
  X, Calendar, User, Heart, DollarSign, Target, TrendingUp,
  MapPin, Users, Star, Clock, ArrowRight, Award, Shield,
  Globe
} from 'lucide-react';
import api from '../services/api';

interface Statistics {
  totalEvents: number;
  totalCampaigns: number;
  totalParticipants: number;
  totalDonations: number;
}

interface FeaturedEvent {
  _id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  location: string;
  maxParticipants: number;
  participantCount: number;
  averageRating: number;
  status: string;
  category: string;
  organizerName: string;
}

interface FeaturedCampaign {
  _id: string;
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  status: string;
  category: string;
  organizationName: string;
  progressPercentage: number;
  totalDonors: number;
  startDate: string;
  endDate: string;
}



interface Donation {
  _id: string;
  name: string;
  email: string;
  amount: number;
  message: string;
  createdAt: string;
  campaign: {
    _id: string;
    title: string;
  };
}

interface Volunteer {
  _id: string;
  name: string;
  email: string;
  createdAt: string;
}

interface HomeData {
  statistics: Statistics;
  featuredEvents: FeaturedEvent[];
  featuredCampaigns: FeaturedCampaign[];
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<HomeData | null>(null);

  // Modal states
  const [showDonationsModal, setShowDonationsModal] = useState(false);
  const [showVolunteersModal, setShowVolunteersModal] = useState(false);
  const [donations, setDonations] = useState<Donation[]>([]);
  const [volunteers, setVolunteers] = useState<Volunteer[]>([]);
  const [modalLoading, setModalLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch real data from multiple API endpoints
        console.log('🏠 Fetching homepage data from API...');

        try {
          // Fetch data from separate endpoints since /api/homepage/data doesn't exist
          const [statisticsRes, eventsRes, campaignsRes] = await Promise.all([
            api.get('/api/home/<USER>').catch(() => ({ data: { success: false } })),
            api.get('/api/home/<USER>').catch(() => ({ data: { success: false } })),
            api.get('/api/home/<USER>').catch(() => ({ data: { success: false } }))
          ]);

          // Combine data from all endpoints
          const homeData: HomeData = {
            statistics: statisticsRes.data.success ? statisticsRes.data.data : {
              totalEvents: 0,
              totalCampaigns: 0,
              totalParticipants: 0,
              totalDonations: 0
            },
            featuredEvents: eventsRes.data.success ? eventsRes.data.data : [],
            featuredCampaigns: campaignsRes.data.success ? campaignsRes.data.data : []
          };

          console.log('✅ Homepage data received:', homeData);
          setData(homeData);

        } catch (apiError: any) {
          console.warn('⚠️ API Error, using fallback empty data:', apiError);

          // Fallback to empty data structure
          const fallbackData: HomeData = {
            statistics: {
              totalEvents: 0,
              totalCampaigns: 0,
              totalParticipants: 0,
              totalDonations: 0
            },
            featuredEvents: [],
            featuredCampaigns: []
          };

          setData(fallbackData);
          const errorMessage = apiError?.response?.data?.message || 'Không thể kết nối đến server';
          console.error(`❌ Homepage API Error: ${errorMessage}`);
        }

      } catch (err) {
        console.error('❌ Error loading homepage data:', err);
        setError('Không thể tải dữ liệu. Vui lòng thử lại sau.');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // Fetch donations list
  const fetchDonations = async () => {
    try {
      setModalLoading(true);
      console.log('💰 Fetching donations list...');
      const response = await api.get('/api/home/<USER>');
      if (response.data.success) {
        setDonations(response.data.data);
        console.log('✅ Donations loaded:', response.data.data.length);
      }
    } catch (error) {
      console.error('❌ Error fetching donations:', error);
    } finally {
      setModalLoading(false);
    }
  };

  // Fetch volunteers list
  const fetchVolunteers = async () => {
    try {
      setModalLoading(true);
      console.log('👥 Fetching volunteers list...');
      const response = await api.get('/api/home/<USER>');
      if (response.data.success) {
        setVolunteers(response.data.data);
        console.log('✅ Volunteers loaded:', response.data.data.length);
      }
    } catch (error) {
      console.error('❌ Error fetching volunteers:', error);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle card clicks
  const handleCardClick = (type: string) => {
    const token = localStorage.getItem('token');
    if (!token) {
      // Redirect to login if not authenticated
      navigate('/login');
      return;
    }

    switch (type) {
      case 'events':
        navigate('/events');
        break;
      case 'campaigns':
        navigate('/campaigns');
        break;
      case 'donations':
        setShowDonationsModal(true);
        fetchDonations();
        break;
      case 'volunteers':
        setShowVolunteersModal(true);
        fetchVolunteers();
        break;
    }
  };



  // Banner slides
  const bannerSlides = [
    {
      id: 'banner1',
      image: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
      title: t('banner.joinCommunity'),
      description: t('banner.joinDesc'),
      link: '/events',
    },
    {
      id: 'banner2',
      image: 'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
      title: t('campaigns.supportTitle'),
      description: t('campaigns.supportDesc'),
      link: '/campaigns',
    },
    {
      id: 'banner3',
      image: 'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
      title: t('banner.aboutUs'),
      description: t('banner.aboutDesc'),
      link: '/events',
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 mb-4">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
        >
          {t('home.tryAgain')}
        </button>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <motion.div 
      className="space-y-16 py-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Banner Section - Slider */}
      <motion.section 
        className="max-w-6xl mx-auto px-4"
        variants={itemVariants}
      >
        <Slider slides={bannerSlides} autoPlayInterval={5000} />
      </motion.section>

      {/* Statistics Section */}
      {data.statistics && (
        <motion.section
          className="max-w-6xl mx-auto px-4 grid md:grid-cols-4 gap-6 text-center"
          variants={itemVariants}
        >
          <motion.div
            className="bg-white dark:bg-slate-800 rounded-xl shadow-lg dark:shadow-slate-900/50 p-6 flex flex-col items-center transform hover:scale-105 transition-all duration-300 cursor-pointer border border-gray-100 dark:border-slate-700"
            whileHover={{ y: -5 }}
            onClick={() => handleCardClick('events')}
          >
            <div className="text-4xl font-bold text-primary mb-2">
              {typeof data.statistics.totalEvents === 'number' ? data.statistics.totalEvents.toLocaleString() : '0'}
            </div>
            <div className="text-muted-foreground">{t('home.totalEvents')}</div>
          </motion.div>
          <motion.div
            className="bg-white dark:bg-slate-800 rounded-xl shadow-lg dark:shadow-slate-900/50 p-6 flex flex-col items-center transform hover:scale-105 transition-all duration-300 cursor-pointer border border-gray-100 dark:border-slate-700"
            whileHover={{ y: -5 }}
            onClick={() => handleCardClick('donations')}
          >
            <div className="text-4xl font-bold text-primary mb-2">
              {typeof data.statistics.totalDonations === 'number' ? formatCurrency(data.statistics.totalDonations) : formatCurrency(0)}
            </div>
            <div className="text-muted-foreground">{t('home.totalDonations')}</div>
          </motion.div>
          <motion.div
            className="bg-white dark:bg-slate-800 rounded-xl shadow-lg dark:shadow-slate-900/50 p-6 flex flex-col items-center transform hover:scale-105 transition-all duration-300 cursor-pointer border border-gray-100 dark:border-slate-700"
            whileHover={{ y: -5 }}
            onClick={() => handleCardClick('volunteers')}
          >
            <div className="text-4xl font-bold text-primary mb-2">
              {typeof data.statistics.totalParticipants === 'number' ? data.statistics.totalParticipants.toLocaleString() : '0'}
            </div>
            <div className="text-muted-foreground">{t('home.totalVolunteers')}</div>
          </motion.div>
          <motion.div
            className="bg-white dark:bg-slate-800 rounded-xl shadow-lg dark:shadow-slate-900/50 p-6 flex flex-col items-center transform hover:scale-105 transition-all duration-300 cursor-pointer border border-gray-100 dark:border-slate-700"
            whileHover={{ y: -5 }}
            onClick={() => handleCardClick('campaigns')}
          >
            <div className="text-4xl font-bold text-primary mb-2">
              {typeof data.statistics.totalCampaigns === 'number' ? data.statistics.totalCampaigns.toLocaleString() : '0'}
            </div>
            <div className="text-muted-foreground">{t('home.totalCampaigns')}</div>
          </motion.div>
        </motion.section>
      )}

      {/* Featured Events Section */}
      {data.featuredEvents && data.featuredEvents.length > 0 && (
        <motion.section
          className="max-w-6xl mx-auto px-4 py-12"
          variants={itemVariants}
        >
          <div className="text-center mb-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent mb-4">
                {t('home.featuredEvents')}
              </h2>
              <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 mx-auto mb-4 rounded-full"></div>
              <p className="text-lg text-gray-600 dark:text-slate-400 max-w-2xl mx-auto mb-6">
                {t('home.featuredEventsDesc')}
              </p>
              <Link
                to="/events"
                className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white px-6 py-3 rounded-full font-semibold hover:from-indigo-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                {t('events.title')}
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {data.featuredEvents.slice(0, 6).map((event, index) => (
              <motion.div
                key={event._id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <Link
                  to={`/events/${event._id}`}
                  className="block rounded-2xl bg-white dark:bg-slate-800 overflow-hidden hover:shadow-2xl dark:hover:shadow-slate-900/50 transition-all duration-500 border border-gray-100 dark:border-slate-700 h-full"
                >
                  <div className="aspect-video relative overflow-hidden bg-gradient-to-br from-indigo-500 via-blue-500 to-purple-600">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

                    {/* Status Badge */}
                    <div className="absolute top-4 left-4">
                      <span className={`px-3 py-1.5 rounded-full text-xs font-semibold text-white backdrop-blur-sm ${
                        event.status === 'upcoming' ? 'bg-green-500/90' :
                        event.status === 'ongoing' ? 'bg-indigo-500/90' : 'bg-gray-500/90'
                      } shadow-lg`}>
                        {event.status === 'upcoming' ? '🚀 ' + t('events.statusUpcoming') :
                         event.status === 'ongoing' ? '⚡ ' + t('events.statusOngoing') : '✅ ' + t('events.statusCompleted')}
                      </span>
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-4 right-4">
                      <span className="px-3 py-1.5 bg-white/20 backdrop-blur-sm text-white text-xs font-medium rounded-full border border-white/30">
                        {event.category}
                      </span>
                    </div>

                    {/* Bottom Info */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between text-white text-sm">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-1 bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full">
                            <Users className="w-4 h-4" />
                            <span className="font-medium">{event.participantCount}/{event.maxParticipants}</span>
                          </div>
                          {event.averageRating > 0 && (
                            <div className="flex items-center space-x-1 bg-yellow-500/20 backdrop-blur-sm px-2 py-1 rounded-full">
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium">{event.averageRating.toFixed(1)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-indigo-600/20 to-blue-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-xs text-gray-500 dark:text-slate-400 font-medium">{event.organizerName}</span>
                      <div className="w-2 h-2 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
                    </div>

                    <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-slate-100 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-indigo-600 group-hover:to-blue-600 group-hover:bg-clip-text transition-all duration-300 line-clamp-2 leading-tight">
                      {event.title}
                    </h3>

                    <p className="text-gray-600 dark:text-slate-400 mb-4 line-clamp-2 leading-relaxed">{event.description}</p>

                    <div className="space-y-3">
                      <div className="flex items-center text-sm text-gray-500 dark:text-slate-400">
                        <div className="w-8 h-8 bg-indigo-50 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mr-3">
                          <Calendar className="w-4 h-4 text-indigo-500" />
                        </div>
                        <span className="font-medium">{new Date(event.startDate).toLocaleDateString('vi-VN')} - {new Date(event.endDate).toLocaleDateString('vi-VN')}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-500 dark:text-slate-400">
                        <div className="w-8 h-8 bg-blue-50 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                          <MapPin className="w-4 h-4 text-blue-500" />
                        </div>
                        <span className="line-clamp-1 font-medium">{event.location}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.section>
      )}

      {/* Featured Campaigns Section */}
      {data.featuredCampaigns && data.featuredCampaigns.length > 0 && (
        <motion.section
          className="max-w-6xl mx-auto px-4 py-12"
          variants={itemVariants}
        >
          <div className="text-center mb-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-4">
                {t('home.featuredCampaigns')}
              </h2>
              <div className="w-20 h-1 bg-gradient-to-r from-emerald-500 to-teal-500 mx-auto mb-4 rounded-full"></div>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
                {t('home.featuredCampaignsDesc')}
              </p>
              <Link
                to="/campaigns"
                className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-6 py-3 rounded-full font-semibold hover:from-emerald-600 hover:to-teal-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                {t('campaigns.title')}
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {data.featuredCampaigns.slice(0, 6).map((campaign, index) => (
              <motion.div
                key={campaign._id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <Link
                  to={`/campaigns/${campaign._id}`}
                  className="block rounded-2xl bg-white overflow-hidden hover:shadow-2xl transition-all duration-500 border border-gray-100 h-full"
                >
                  <div className="aspect-video relative overflow-hidden bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

                    {/* Status Badge */}
                    <div className="absolute top-4 left-4">
                      <span className={`px-3 py-1.5 rounded-full text-xs font-semibold text-white backdrop-blur-sm shadow-lg ${
                        campaign.status === 'active' ? 'bg-emerald-500/90' :
                        campaign.status === 'completed' ? 'bg-teal-500/90' : 'bg-gray-500/90'
                      }`}>
                        {campaign.status === 'active' ? '💚 ' + t('campaigns.statusActive') :
                         campaign.status === 'completed' ? '🎉 ' + t('campaigns.statusCompleted') : '⏰ ' + t('campaigns.statusEnded')}
                      </span>
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-4 right-4">
                      <span className="px-3 py-1.5 bg-white/20 backdrop-blur-sm text-white text-xs font-medium rounded-full border border-white/30">
                        {campaign.category}
                      </span>
                    </div>

                    {/* Bottom Info */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between text-white text-sm">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-1 bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full">
                            <Heart className="w-4 h-4" />
                            <span className="font-medium">{campaign.totalDonors} {t('home.supporters')}</span>
                          </div>
                          <div className="flex items-center space-x-1 bg-emerald-500/20 backdrop-blur-sm px-2 py-1 rounded-full">
                            <TrendingUp className="w-4 h-4" />
                            <span className="font-medium">{Math.round(campaign.progressPercentage)}%</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-emerald-600/20 to-teal-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-xs text-gray-500 font-medium">{campaign.organizationName}</span>
                      <div className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full"></div>
                    </div>

                    <h3 className="text-xl font-bold mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-emerald-600 group-hover:to-teal-600 group-hover:bg-clip-text transition-all duration-300 line-clamp-2 leading-tight">
                      {campaign.title}
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-2 leading-relaxed">{campaign.description}</p>

                    {/* Enhanced Progress Bar */}
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-bold text-emerald-600">
                          {formatCurrency(campaign.currentAmount)}
                        </span>
                        <span className="text-sm text-gray-500 font-medium">
                          {formatCurrency(campaign.targetAmount)}
                        </span>
                      </div>
                      <div className="relative w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                        <div
                          className="absolute top-0 left-0 h-full bg-gradient-to-r from-emerald-500 via-teal-500 to-emerald-600 rounded-full transition-all duration-700 ease-out shadow-sm"
                          style={{ width: `${Math.min(100, campaign.progressPercentage)}%` }}
                        >
                          <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-600 mt-2 font-medium">
                        🎯 {t('home.achieved')} {Math.round(campaign.progressPercentage)}% {t('home.target')}
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-emerald-50 rounded-full flex items-center justify-center mr-2">
                          <Clock className="w-4 h-4 text-emerald-500" />
                        </div>
                        <span className="font-medium">{new Date(campaign.endDate).toLocaleDateString('vi-VN')}</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-teal-50 rounded-full flex items-center justify-center mr-2">
                          <Target className="w-4 h-4 text-teal-500" />
                        </div>
                        <span className="font-medium">{campaign.totalDonors} {t('home.people')}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.section>
      )}



      {/* Donations Modal */}
      <AnimatePresence>
        {showDonationsModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowDonationsModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <DollarSign className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">{t('home.donationsList')}</h3>
                </div>
                <button
                  onClick={() => setShowDonationsModal(false)}
                  className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-all duration-200"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[60vh]">
                {modalLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
                  </div>
                ) : donations.length > 0 ? (
                  <div className="space-y-4">
                    {donations.map((donation, index) => (
                      <motion.div
                        key={donation._id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors duration-200"
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                                <Heart className="w-4 h-4 text-white" />
                              </div>
                              <div>
                                <h4 className="font-semibold text-gray-800">{donation.name}</h4>
                                <p className="text-gray-500 text-sm">{donation.email}</p>
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              <span className="font-medium">{t('home.campaign')}:</span> {donation.campaign.title}
                            </p>
                            {donation.message && (
                              <div className="bg-white rounded-lg p-3 mt-2">
                                <p className="text-gray-700 italic text-sm">"{donation.message}"</p>
                              </div>
                            )}
                            <p className="text-xs text-gray-500 mt-2">
                              {new Date(donation.createdAt).toLocaleDateString('vi-VN', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                          </div>
                          <div className="text-right ml-4">
                            <p className="text-xl font-bold text-green-600">
                              {formatCurrency(donation.amount)}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <DollarSign className="w-10 h-10 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-700 mb-2">{t('home.noDonations')}</h3>
                    <p className="text-gray-500">{t('home.noDonationsDesc')}</p>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Volunteers Modal */}
      <AnimatePresence>
        {showVolunteersModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowVolunteersModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">{t('home.volunteersList')}</h3>
                </div>
                <button
                  onClick={() => setShowVolunteersModal(false)}
                  className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-all duration-200"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[60vh]">
                {modalLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                  </div>
                ) : volunteers.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {volunteers.map((volunteer, index) => (
                      <motion.div
                        key={volunteer._id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors duration-200"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                            <User className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-800">{volunteer.name}</h4>
                            <p className="text-gray-500 text-sm">{volunteer.email}</p>
                            <p className="text-xs text-gray-400 mt-1">
                              Tham gia: {new Date(volunteer.createdAt).toLocaleDateString('vi-VN')}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <User className="w-10 h-10 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-700 mb-2">{t('home.noVolunteers')}</h3>
                    <p className="text-gray-500">{t('home.noVolunteersDesc')}</p>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* About Website Section */}
      <motion.section
        className="relative py-16 overflow-hidden"
        variants={itemVariants}
      >
        {/* Background with gradient and pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
          <div className="absolute inset-0 opacity-30">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                               radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.1) 0%, transparent 50%)`
            }}></div>
          </div>
        </div>

        <div className="relative max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-5xl font-extrabold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6 leading-tight">
                {t('banner.aboutUs')}
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 mx-auto mb-6 rounded-full"></div>
              <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed font-medium">
                {t('banner.aboutDesc')}
              </p>
            </motion.div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              className="group relative"
              whileHover={{ y: -8 }}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 h-full">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Globe className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">{t('about.connectCommunity')}</h3>
                <p className="text-gray-600 text-center leading-relaxed">
                  {t('about.connectDesc')}
                </p>
              </div>
            </motion.div>

            <motion.div
              className="group relative"
              whileHover={{ y: -8 }}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 h-full">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Shield className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">{t('about.transparent')}</h3>
                <p className="text-gray-600 text-center leading-relaxed">
                  {t('about.transparentDesc')}
                </p>
              </div>
            </motion.div>

            <motion.div
              className="group relative"
              whileHover={{ y: -8 }}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 h-full">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Award className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-purple-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">{t('about.positiveImpact')}</h3>
                <p className="text-gray-600 text-center leading-relaxed">
                  {t('about.positiveImpactDesc')}
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>
    </motion.div>
  );
};

export default HomePage;